"""炼丹

Revision ID: 1934aaff469c
Revises: 83484577519d
Create Date: 2025-07-06 19:27:42.582707

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '1934aaff469c'
down_revision: Union[str, None] = '83484577519d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_player_donation_log_donate_date', table_name='player_donation_log')
    op.drop_index('ix_player_donation_log_player_id', table_name='player_donation_log')
    op.drop_table('player_donation_log')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('player_donation_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('donate_date', sa.DATE(), server_default=sa.text('(CURRENT_DATE)'), nullable=False),
    sa.Column('amount', sa.INTEGER(), server_default=sa.text("'0'"), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_player_donation_log_player_id', 'player_donation_log', ['player_id'], unique=False)
    op.create_index('ix_player_donation_log_donate_date', 'player_donation_log', ['donate_date'], unique=False)
    # ### end Alembic commands ###
