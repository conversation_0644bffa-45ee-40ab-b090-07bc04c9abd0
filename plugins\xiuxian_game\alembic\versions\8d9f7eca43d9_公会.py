"""公会

Revision ID: 8d9f7eca43d9
Revises: c2c5232c543e
Create Date: 2025-07-05 19:18:47.839537

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8d9f7eca43d9'
down_revision: Union[str, None] = 'c2c5232c543e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('guild',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(length=32), nullable=False, comment='公会名称'),
    sa.Column('president_id', sa.String(length=32), nullable=False, comment='会长的玩家平台ID'),
    sa.Column('created_at', sa.DateTime(), server_default=sa.text('(CURRENT_TIMESTAMP)'), nullable=False, comment='创建时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('name')
    )
    op.add_column('player', sa.Column('guild_id', sa.Integer(), nullable=True, comment='所属公会ID'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player', 'guild_id')
    op.drop_table('guild')
    # ### end Alembic commands ###
