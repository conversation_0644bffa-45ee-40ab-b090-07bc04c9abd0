"""set inventory_capacity default to 100 & migrate old data

Revision ID: c2c5232c543e
Revises: 476f84c64b88
Create Date: 2025-07-05 01:59:02.760532

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'c2c5232c543e'
down_revision: Union[str, None] = '476f84c64b88'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
