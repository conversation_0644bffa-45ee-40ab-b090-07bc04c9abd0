# 游戏基础配置
game:
  max_level: 300  # 最大等级
  base_exp: 10   # 基础经验值（1级到2级所需经验）
  points_per_level: 10  # 每次升级获得的属性点数
  exp_curve:
    early_multiplier: 10
    late_multiplier: 6
    curve_change_level: 10
  inventory_page_size: 10  # 背包每页显示物品数量

  # 冥返机制配置
  reincarnation:
    cultivation_efficiency_bonus: 0.5  # 每转修炼效率加成（50%*转数）
    seclusion_efficiency_bonus: 0.5   # 每转闭关效率加成（50%*转数）

# 资源控制配置
resource_control:
  # 材料掉落基础概率（会根据等级和稀有度调整）
  base_drop_rates:
    white: 0.30    # 白色材料基础掉落率
    green: 0.15    # 绿色材料基础掉落率
    blue: 0.08     # 蓝色材料基础掉落率
    purple: 0.03   # 紫色材料基础掉落率
    orange: 0.01   # 橙色材料基础掉落率

  # 不同等级段的资源获取倍率
  level_multipliers:
    1-20: 1.0      # 新手期：正常获取
    21-60: 0.9     # 成长期：略微减少，鼓励探索高级区域
    61-100: 0.8    # 进阶期：进一步减少低级材料获取
    101+: 0.7      # 高手期：大幅减少低级材料，专注高级材料

  # 商人价格倍率（相对于基础价格）
  merchant_price_range: [1.2, 1.5]  # 商人价格为基础价格的1.2-1.5倍


# 修炼基础配置
cultivate:
    cool_down: 300  # 冷却时间（秒）
    base_exp: 50   # 基础经验值
    breakthrough:
        change_level: 30  # 多高等级之后才可能失败
        fail_keep_ratio: 0.7  # 失败保留经验比例
        guardian_cost: 50  # 怨念值护航消耗
        guardian_cost_base: 50  # 护航基础怨念值消耗
        guardian_cost_per_level: 10  # 每级额外消耗
        base_success_rate: 0.8  # change_level 时的基础成功率
        decay_per_level: 0.005   # 每提高一级成功率递减值
        min_success_rate: 0.05    # 最低成功率下限
    attributes_base:
        max_hp: 1    # 体 (max_hp) 基础增量
        max_mp: 1    # 智 (max_mp) 基础增量
        attack: 1     # 力 (attack) 基础增量
        defense: 1    # 防 (defense) 基础增量
        agility: 1    # 敏 (agility) 基础增量
        luck: 1       # 运 (luck) 基础增量

# 月卡配置
monthly_card:
  basic:
    name: "基础月卡"
    cultivation_efficiency: 0.2      # 修炼效率+20%
    seclusion_time_limit: 9          # 闭关时长上限9小时
    seclusion_efficiency: 0.2        # 闭关效率+20%
    inventory_bonus: 5               # 背包格子+5
    auto_collect: false              # 不自动采集
    sign_stamina_bonus: 5            # 签到咒抗上限增加+5
    sign_sweep_tokens: 1             # 签到获得扫荡令数量
  premium:
    name: "高级月卡"
    cultivation_efficiency: 0.5      # 修炼效率+50%
    seclusion_time_limit: 12         # 闭关时长上限12小时
    seclusion_efficiency: 0.5        # 闭关效率+50%
    inventory_bonus: 20              # 背包格子+20
    auto_collect: true               # 自动采集材料
    sign_stamina_bonus: 5            # 签到咒抗上限增加+5
    sign_sweep_tokens: 3             # 签到获得扫荡令数量