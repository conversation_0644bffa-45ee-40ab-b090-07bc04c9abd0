"""跑商

Revision ID: 2f21018cce59
Revises: 1934aaff469c
Create Date: 2025-07-09 15:27:41.765063

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '2f21018cce59'
down_revision: Union[str, None] = '1934aaff469c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('navigation_task',
    sa.Column('player_id', sa.String(length=32), nullable=False, comment='玩家ID'),
    sa.Column('target_x', sa.Integer(), nullable=False, comment='目标X'),
    sa.Column('target_y', sa.Integer(), nullable=False, comment='目标Y'),
    sa.Column('last_update', sa.DateTime(), nullable=False, comment='上次位置更新时间'),
    sa.PrimaryKeyConstraint('player_id')
    )
    op.drop_index('ix_elixir_usage_log_player_id', table_name='elixir_usage_log')
    op.drop_index('ix_elixir_usage_log_use_date', table_name='elixir_usage_log')
    op.drop_table('elixir_usage_log')
    op.drop_index('ix_player_donation_log_donate_date', table_name='player_donation_log')
    op.drop_index('ix_player_donation_log_player_id', table_name='player_donation_log')
    op.drop_table('player_donation_log')
    op.drop_index('ix_alchemy_task_finish_at', table_name='alchemy_task')
    op.drop_index('ix_alchemy_task_player_id', table_name='alchemy_task')
    op.drop_table('alchemy_task')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('alchemy_task',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('furnace_id', sa.INTEGER(), nullable=True),
    sa.Column('recipe_key', sa.VARCHAR(length=64), nullable=False),
    sa.Column('qty', sa.INTEGER(), nullable=False),
    sa.Column('success_rate', sa.FLOAT(), nullable=False),
    sa.Column('produce_item_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('produce_per', sa.INTEGER(), nullable=False),
    sa.Column('finish_at', sa.DATETIME(), nullable=False),
    sa.Column('delivered', sa.BOOLEAN(), server_default=sa.text("'0'"), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.ForeignKeyConstraint(['furnace_id'], ['equipment_instances.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_alchemy_task_player_id', 'alchemy_task', ['player_id'], unique=False)
    op.create_index('ix_alchemy_task_finish_at', 'alchemy_task', ['finish_at'], unique=False)
    op.create_table('player_donation_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('donate_date', sa.DATE(), server_default=sa.text('(CURRENT_DATE)'), nullable=False),
    sa.Column('amount', sa.INTEGER(), server_default=sa.text("'0'"), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_player_donation_log_player_id', 'player_donation_log', ['player_id'], unique=False)
    op.create_index('ix_player_donation_log_donate_date', 'player_donation_log', ['donate_date'], unique=False)
    op.create_table('elixir_usage_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('item_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('use_date', sa.DATE(), nullable=False),
    sa.Column('quantity', sa.INTEGER(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_elixir_usage_log_use_date', 'elixir_usage_log', ['use_date'], unique=False)
    op.create_index('ix_elixir_usage_log_player_id', 'elixir_usage_log', ['player_id'], unique=False)
    op.drop_table('navigation_task')
    # ### end Alembic commands ###
