"""连续签到

Revision ID: f1aececaab00
Revises: 86c239d5382d
Create Date: 2025-07-12 00:09:16.699132

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'f1aececaab00'
down_revision: Union[str, None] = '86c239d5382d'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index('ix_alchemy_task_finish_at', table_name='alchemy_task')
    op.drop_index('ix_alchemy_task_player_id', table_name='alchemy_task')
    op.drop_table('alchemy_task')
    op.drop_index('ix_elixir_usage_log_player_id', table_name='elixir_usage_log')
    op.drop_index('ix_elixir_usage_log_use_date', table_name='elixir_usage_log')
    op.drop_table('elixir_usage_log')
    op.drop_index('ix_player_donation_log_donate_date', table_name='player_donation_log')
    op.drop_index('ix_player_donation_log_player_id', table_name='player_donation_log')
    op.drop_table('player_donation_log')
    op.add_column('player', sa.Column('sign_streak', sa.Integer(), server_default='0', nullable=False, comment='连续签到天数'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player', 'sign_streak')
    op.create_table('player_donation_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('donate_date', sa.DATE(), server_default=sa.text('(CURRENT_DATE)'), nullable=False),
    sa.Column('amount', sa.INTEGER(), server_default=sa.text("'0'"), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_player_donation_log_player_id', 'player_donation_log', ['player_id'], unique=False)
    op.create_index('ix_player_donation_log_donate_date', 'player_donation_log', ['donate_date'], unique=False)
    op.create_table('elixir_usage_log',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('item_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('use_date', sa.DATE(), nullable=False),
    sa.Column('quantity', sa.INTEGER(), nullable=False),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_elixir_usage_log_use_date', 'elixir_usage_log', ['use_date'], unique=False)
    op.create_index('ix_elixir_usage_log_player_id', 'elixir_usage_log', ['player_id'], unique=False)
    op.create_table('alchemy_task',
    sa.Column('id', sa.INTEGER(), nullable=False),
    sa.Column('player_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('furnace_id', sa.INTEGER(), nullable=True),
    sa.Column('recipe_key', sa.VARCHAR(length=64), nullable=False),
    sa.Column('qty', sa.INTEGER(), nullable=False),
    sa.Column('success_rate', sa.FLOAT(), nullable=False),
    sa.Column('produce_item_id', sa.VARCHAR(length=32), nullable=False),
    sa.Column('produce_per', sa.INTEGER(), nullable=False),
    sa.Column('finish_at', sa.DATETIME(), nullable=False),
    sa.Column('delivered', sa.BOOLEAN(), server_default=sa.text("'0'"), nullable=False),
    sa.Column('created_at', sa.DATETIME(), nullable=False),
    sa.ForeignKeyConstraint(['furnace_id'], ['equipment_instances.id'], ondelete='SET NULL'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_index('ix_alchemy_task_player_id', 'alchemy_task', ['player_id'], unique=False)
    op.create_index('ix_alchemy_task_finish_at', 'alchemy_task', ['finish_at'], unique=False)
    # ### end Alembic commands ###
