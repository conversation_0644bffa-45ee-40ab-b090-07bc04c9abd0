"""公会fix

Revision ID: 144285518fa2
Revises: 8d9f7eca43d9
Create Date: 2025-07-05 19:43:17.855565

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '144285518fa2'
down_revision: Union[str, None] = '8d9f7eca43d9'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('guild', sa.Column('level', sa.Integer(), server_default='1', nullable=False, comment='公会等级'))
    op.add_column('guild', sa.Column('exp', sa.Integer(), server_default='0', nullable=False, comment='公会经验/活跃度'))
    op.add_column('guild', sa.Column('notice', sa.String(length=128), nullable=True, comment='公会公告'))
    op.add_column('player', sa.Column('guild_contribution', sa.Integer(), server_default='0', nullable=False, comment='公会贡献值'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('player', 'guild_contribution')
    op.drop_column('guild', 'notice')
    op.drop_column('guild', 'exp')
    op.drop_column('guild', 'level')
    # ### end Alembic commands ###
